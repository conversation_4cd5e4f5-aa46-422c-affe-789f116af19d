<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1749501712371" clover="3.2.0">
  <project timestamp="1749501712371" name="All files">
    <metrics statements="258" coveredstatements="147" conditionals="121" coveredconditionals="86" methods="28" coveredmethods="15" elements="407" coveredelements="248" complexity="0" loc="258" ncloc="258" packages="1" files="1" classes="1"/>
    <file name="index.ts" path="/home/<USER>/Workspace/dbos/dbos-kyc-demo/src/index.ts">
      <metrics statements="258" coveredstatements="147" conditionals="121" coveredconditionals="86" methods="28" coveredmethods="15"/>
      <line num="1" count="5" type="stmt"/>
      <line num="2" count="5" type="stmt"/>
      <line num="65" count="5" type="stmt"/>
      <line num="66" count="5" type="stmt"/>
      <line num="69" count="5" type="stmt"/>
      <line num="74" count="5" type="stmt"/>
      <line num="79" count="5" type="stmt"/>
      <line num="84" count="5" type="stmt"/>
      <line num="111" count="5" type="stmt"/>
      <line num="115" count="5" type="cond" truecount="3" falsecount="1"/>
      <line num="116" count="2" type="stmt"/>
      <line num="119" count="2" type="stmt"/>
      <line num="122" count="2" type="cond" truecount="3" falsecount="0"/>
      <line num="123" count="1" type="stmt"/>
      <line num="124" count="1" type="stmt"/>
      <line num="127" count="1" type="stmt"/>
      <line num="128" count="1" type="stmt"/>
      <line num="132" count="5" type="cond" truecount="3" falsecount="1"/>
      <line num="133" count="2" type="stmt"/>
      <line num="135" count="2" type="stmt"/>
      <line num="138" count="2" type="stmt"/>
      <line num="140" count="2" type="stmt"/>
      <line num="141" count="6" type="stmt"/>
      <line num="142" count="6" type="stmt"/>
      <line num="144" count="6" type="cond" truecount="1" falsecount="0"/>
      <line num="146" count="1" type="stmt"/>
      <line num="152" count="1" type="cond" truecount="1" falsecount="0"/>
      <line num="153" count="1" type="stmt"/>
      <line num="166" count="2" type="stmt"/>
      <line num="167" count="2" type="stmt"/>
      <line num="171" count="5" type="stmt"/>
      <line num="177" count="0" type="stmt"/>
      <line num="180" count="0" type="stmt"/>
      <line num="181" count="0" type="stmt"/>
      <line num="183" count="0" type="stmt"/>
      <line num="184" count="0" type="stmt"/>
      <line num="185" count="0" type="stmt"/>
      <line num="191" count="0" type="stmt"/>
      <line num="196" count="0" type="stmt"/>
      <line num="197" count="0" type="stmt"/>
      <line num="200" count="0" type="cond" truecount="0" falsecount="3"/>
      <line num="201" count="0" type="stmt"/>
      <line num="202" count="0" type="stmt"/>
      <line num="206" count="0" type="stmt"/>
      <line num="210" count="5" type="cond" truecount="3" falsecount="1"/>
      <line num="211" count="2" type="stmt"/>
      <line num="214" count="2" type="stmt"/>
      <line num="216" count="2" type="stmt"/>
      <line num="217" count="2" type="stmt"/>
      <line num="219" count="2" type="cond" truecount="1" falsecount="0"/>
      <line num="220" count="1" type="stmt"/>
      <line num="224" count="2" type="cond" truecount="1" falsecount="0"/>
      <line num="225" count="1" type="stmt"/>
      <line num="229" count="2" type="stmt"/>
      <line num="234" count="5" type="cond" truecount="3" falsecount="1"/>
      <line num="235" count="2" type="stmt"/>
      <line num="238" count="2" type="stmt"/>
      <line num="241" count="2" type="stmt"/>
      <line num="242" count="2" type="stmt"/>
      <line num="243" count="2" type="stmt"/>
      <line num="245" count="2" type="cond" truecount="2" falsecount="0"/>
      <line num="249" count="2" type="stmt"/>
      <line num="251" count="2" type="cond" truecount="2" falsecount="0"/>
      <line num="252" count="2" type="stmt"/>
      <line num="256" count="5" type="cond" truecount="3" falsecount="1"/>
      <line num="257" count="1" type="stmt"/>
      <line num="260" count="1" type="stmt"/>
      <line num="262" count="1" type="stmt"/>
      <line num="265" count="1" type="stmt"/>
      <line num="266" count="1" type="cond" truecount="1" falsecount="1"/>
      <line num="267" count="1" type="cond" truecount="0" falsecount="1"/>
      <line num="270" count="1" type="stmt"/>
      <line num="271" count="1" type="stmt"/>
      <line num="272" count="3" type="cond" truecount="2" falsecount="1"/>
      <line num="273" count="0" type="stmt"/>
      <line num="277" count="1" type="stmt"/>
      <line num="279" count="1" type="stmt"/>
      <line num="280" count="1" type="stmt"/>
      <line num="284" count="5" type="cond" truecount="3" falsecount="1"/>
      <line num="285" count="2" type="stmt"/>
      <line num="288" count="2" type="stmt"/>
      <line num="291" count="2" type="stmt"/>
      <line num="292" count="2" type="stmt"/>
      <line num="294" count="2" type="stmt"/>
      <line num="299" count="2" type="cond" truecount="2" falsecount="0"/>
      <line num="300" count="2" type="stmt"/>
      <line num="305" count="5" type="cond" truecount="3" falsecount="1"/>
      <line num="306" count="1" type="stmt"/>
      <line num="309" count="1" type="stmt"/>
      <line num="312" count="1" type="stmt"/>
      <line num="333" count="1" type="stmt"/>
      <line num="334" count="1" type="stmt"/>
      <line num="338" count="5" type="cond" truecount="3" falsecount="1"/>
      <line num="339" count="1" type="stmt"/>
      <line num="341" count="1" type="stmt"/>
      <line num="343" count="1" type="stmt"/>
      <line num="345" count="1" type="stmt"/>
      <line num="346" count="3" type="cond" truecount="1" falsecount="0"/>
      <line num="347" count="3" type="cond" truecount="3" falsecount="0"/>
      <line num="349" count="1" type="stmt"/>
      <line num="350" count="1" type="stmt"/>
      <line num="351" count="1" type="stmt"/>
      <line num="353" count="1" type="stmt"/>
      <line num="354" count="1" type="stmt"/>
      <line num="356" count="1" type="stmt"/>
      <line num="357" count="1" type="stmt"/>
      <line num="362" count="1" type="stmt"/>
      <line num="363" count="1" type="stmt"/>
      <line num="368" count="5" type="cond" truecount="3" falsecount="1"/>
      <line num="374" count="0" type="stmt"/>
      <line num="376" count="0" type="stmt"/>
      <line num="379" count="0" type="stmt"/>
      <line num="380" count="0" type="stmt"/>
      <line num="381" count="0" type="stmt"/>
      <line num="382" count="0" type="stmt"/>
      <line num="384" count="0" type="stmt"/>
      <line num="393" count="5" type="stmt"/>
      <line num="398" count="0" type="stmt"/>
      <line num="400" count="0" type="stmt"/>
      <line num="402" count="0" type="stmt"/>
      <line num="411" count="0" type="stmt"/>
      <line num="412" count="0" type="stmt"/>
      <line num="417" count="5" type="cond" truecount="3" falsecount="1"/>
      <line num="421" count="2" type="stmt"/>
      <line num="424" count="2" type="stmt"/>
      <line num="427" count="2" type="stmt"/>
      <line num="428" count="2" type="cond" truecount="0" falsecount="1"/>
      <line num="429" count="0" type="stmt"/>
      <line num="430" count="0" type="stmt"/>
      <line num="433" count="2" type="stmt"/>
      <line num="436" count="2" type="stmt"/>
      <line num="438" count="2" type="stmt"/>
      <line num="441" count="2" type="cond" truecount="1" falsecount="0"/>
      <line num="442" count="1" type="stmt"/>
      <line num="443" count="1" type="stmt"/>
      <line num="446" count="2" type="stmt"/>
      <line num="448" count="2" type="cond" truecount="2" falsecount="0"/>
      <line num="449" count="2" type="stmt"/>
      <line num="451" count="2" type="stmt"/>
      <line num="455" count="5" type="cond" truecount="3" falsecount="1"/>
      <line num="460" count="2" type="stmt"/>
      <line num="462" count="2" type="stmt"/>
      <line num="465" count="2" type="stmt"/>
      <line num="467" count="2" type="cond" truecount="0" falsecount="1"/>
      <line num="468" count="0" type="stmt"/>
      <line num="469" count="0" type="stmt"/>
      <line num="476" count="2" type="stmt"/>
      <line num="479" count="2" type="stmt"/>
      <line num="481" count="2" type="stmt"/>
      <line num="484" count="2" type="stmt"/>
      <line num="486" count="2" type="cond" truecount="0" falsecount="1"/>
      <line num="487" count="0" type="stmt"/>
      <line num="488" count="0" type="stmt"/>
      <line num="497" count="2" type="stmt"/>
      <line num="499" count="2" type="cond" truecount="2" falsecount="0"/>
      <line num="500" count="1" type="stmt"/>
      <line num="501" count="1" type="stmt"/>
      <line num="502" count="1" type="cond" truecount="1" falsecount="1"/>
      <line num="503" count="0" type="stmt"/>
      <line num="504" count="0" type="stmt"/>
      <line num="506" count="1" type="stmt"/>
      <line num="507" count="1" type="stmt"/>
      <line num="510" count="2" type="stmt"/>
      <line num="511" count="2" type="stmt"/>
      <line num="513" count="2" type="stmt"/>
      <line num="515" count="2" type="stmt"/>
      <line num="519" count="5" type="cond" truecount="3" falsecount="1"/>
      <line num="520" count="0" type="stmt"/>
      <line num="522" count="0" type="stmt"/>
      <line num="525" count="0" type="stmt"/>
      <line num="527" count="0" type="stmt"/>
      <line num="530" count="0" type="stmt"/>
      <line num="532" count="0" type="stmt"/>
      <line num="535" count="0" type="stmt"/>
      <line num="537" count="0" type="stmt"/>
      <line num="540" count="0" type="stmt"/>
      <line num="546" count="0" type="stmt"/>
      <line num="547" count="0" type="stmt"/>
      <line num="549" count="0" type="stmt"/>
      <line num="551" count="0" type="stmt"/>
      <line num="556" count="5" type="cond" truecount="9" falsecount="3"/>
      <line num="557" count="0" type="stmt"/>
      <line num="560" count="0" type="stmt"/>
      <line num="561" count="0" type="stmt"/>
      <line num="564" count="0" type="stmt"/>
      <line num="565" count="0" type="stmt"/>
      <line num="567" count="0" type="stmt"/>
      <line num="572" count="1" type="cond" truecount="1" falsecount="4"/>
      <line num="574" count="1" type="stmt"/>
      <line num="576" count="0" type="stmt"/>
      <line num="578" count="0" type="stmt"/>
      <line num="580" count="0" type="stmt"/>
      <line num="582" count="0" type="stmt"/>
      <line num="588" count="5" type="stmt"/>
      <line num="589" count="0" type="stmt"/>
      <line num="590" count="0" type="stmt"/>
      <line num="593" count="0" type="stmt"/>
      <line num="599" count="0" type="stmt"/>
      <line num="605" count="0" type="stmt"/>
      <line num="606" count="0" type="stmt"/>
      <line num="610" count="5" type="stmt"/>
      <line num="611" count="0" type="stmt"/>
      <line num="612" count="0" type="stmt"/>
      <line num="615" count="0" type="stmt"/>
      <line num="620" count="0" type="stmt"/>
      <line num="626" count="0" type="stmt"/>
      <line num="627" count="0" type="stmt"/>
      <line num="631" count="5" type="stmt"/>
      <line num="632" count="0" type="stmt"/>
      <line num="633" count="0" type="stmt"/>
      <line num="635" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="636" count="0" type="stmt"/>
      <line num="637" count="0" type="stmt"/>
      <line num="641" count="0" type="stmt"/>
      <line num="646" count="0" type="stmt"/>
      <line num="652" count="0" type="stmt"/>
      <line num="653" count="0" type="stmt"/>
      <line num="657" count="5" type="stmt"/>
      <line num="658" count="0" type="stmt"/>
      <line num="659" count="0" type="stmt"/>
      <line num="661" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="662" count="0" type="stmt"/>
      <line num="663" count="0" type="stmt"/>
      <line num="667" count="0" type="stmt"/>
      <line num="668" count="0" type="stmt"/>
      <line num="671" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="672" count="0" type="stmt"/>
      <line num="673" count="0" type="stmt"/>
      <line num="677" count="0" type="stmt"/>
      <line num="678" count="0" type="stmt"/>
      <line num="679" count="0" type="stmt"/>
      <line num="680" count="0" type="stmt"/>
      <line num="681" count="0" type="stmt"/>
      <line num="682" count="0" type="stmt"/>
      <line num="685" count="0" type="stmt"/>
      <line num="688" count="0" type="stmt"/>
      <line num="695" count="0" type="stmt"/>
      <line num="696" count="0" type="stmt"/>
      <line num="700" count="5" type="stmt"/>
      <line num="701" count="0" type="stmt"/>
      <line num="702" count="0" type="stmt"/>
      <line num="704" count="0" type="cond" truecount="0" falsecount="1"/>
      <line num="705" count="0" type="stmt"/>
      <line num="706" count="0" type="stmt"/>
      <line num="710" count="0" type="stmt"/>
      <line num="711" count="0" type="stmt"/>
      <line num="713" count="0" type="stmt"/>
      <line num="718" count="0" type="stmt"/>
      <line num="719" count="0" type="stmt"/>
      <line num="724" count="5" type="stmt"/>
      <line num="725" count="0" type="stmt"/>
      <line num="734" count="5" type="stmt"/>
      <line num="739" count="5" type="stmt"/>
      <line num="741" count="5" type="cond" truecount="2" falsecount="0"/>
      <line num="742" count="5" type="stmt"/>
      <line num="743" count="0" type="stmt"/>
      <line num="744" count="0" type="stmt"/>
      <line num="748" count="5" type="stmt"/>
    </file>
  </project>
</coverage>
