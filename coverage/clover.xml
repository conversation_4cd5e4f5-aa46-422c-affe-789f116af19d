<?xml version="1.0" encoding="UTF-8"?>
<coverage generated="1749523175409" clover="3.2.0">
  <project timestamp="1749523175409" name="All files">
    <metrics statements="386" coveredstatements="47" conditionals="91" coveredconditionals="5" methods="70" coveredmethods="2" elements="547" coveredelements="54" complexity="0" loc="386" ncloc="386" packages="5" files="7" classes="7"/>
    <package name="src">
      <metrics statements="274" coveredstatements="47" conditionals="57" coveredconditionals="5" methods="30" coveredmethods="2"/>
      <file name="index.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/index.ts">
        <metrics statements="274" coveredstatements="47" conditionals="57" coveredconditionals="5" methods="30" coveredmethods="2"/>
        <line num="1" count="1" type="stmt"/>
        <line num="2" count="1" type="stmt"/>
        <line num="3" count="1" type="stmt"/>
        <line num="4" count="1" type="stmt"/>
        <line num="67" count="1" type="stmt"/>
        <line num="68" count="1" type="stmt"/>
        <line num="71" count="1" type="stmt"/>
        <line num="76" count="1" type="stmt"/>
        <line num="81" count="1" type="stmt"/>
        <line num="86" count="1" type="stmt"/>
        <line num="113" count="1" type="stmt"/>
        <line num="117" count="1" type="stmt"/>
        <line num="118" count="2" type="stmt"/>
        <line num="121" count="2" type="stmt"/>
        <line num="124" count="2" type="cond" truecount="3" falsecount="0"/>
        <line num="125" count="1" type="stmt"/>
        <line num="126" count="1" type="stmt"/>
        <line num="129" count="1" type="stmt"/>
        <line num="130" count="1" type="stmt"/>
        <line num="134" count="1" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="137" count="0" type="stmt"/>
        <line num="140" count="0" type="stmt"/>
        <line num="142" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="144" count="0" type="stmt"/>
        <line num="146" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="148" count="0" type="stmt"/>
        <line num="154" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="155" count="0" type="stmt"/>
        <line num="168" count="0" type="stmt"/>
        <line num="169" count="0" type="stmt"/>
        <line num="173" count="1" type="stmt"/>
        <line num="179" count="0" type="stmt"/>
        <line num="182" count="0" type="stmt"/>
        <line num="183" count="0" type="stmt"/>
        <line num="185" count="0" type="stmt"/>
        <line num="186" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="193" count="0" type="stmt"/>
        <line num="198" count="0" type="stmt"/>
        <line num="199" count="0" type="stmt"/>
        <line num="202" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="203" count="0" type="stmt"/>
        <line num="204" count="0" type="stmt"/>
        <line num="208" count="0" type="stmt"/>
        <line num="212" count="1" type="stmt"/>
        <line num="213" count="0" type="stmt"/>
        <line num="216" count="0" type="stmt"/>
        <line num="218" count="0" type="stmt"/>
        <line num="219" count="0" type="stmt"/>
        <line num="221" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="222" count="0" type="stmt"/>
        <line num="226" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="227" count="0" type="stmt"/>
        <line num="231" count="0" type="stmt"/>
        <line num="236" count="1" type="stmt"/>
        <line num="237" count="0" type="stmt"/>
        <line num="240" count="0" type="stmt"/>
        <line num="243" count="0" type="stmt"/>
        <line num="244" count="0" type="stmt"/>
        <line num="245" count="0" type="stmt"/>
        <line num="247" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="251" count="0" type="stmt"/>
        <line num="253" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="254" count="0" type="stmt"/>
        <line num="258" count="1" type="stmt"/>
        <line num="259" count="0" type="stmt"/>
        <line num="262" count="0" type="stmt"/>
        <line num="264" count="0" type="stmt"/>
        <line num="267" count="0" type="stmt"/>
        <line num="268" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="269" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="272" count="0" type="stmt"/>
        <line num="273" count="0" type="stmt"/>
        <line num="274" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="275" count="0" type="stmt"/>
        <line num="279" count="0" type="stmt"/>
        <line num="281" count="0" type="stmt"/>
        <line num="282" count="0" type="stmt"/>
        <line num="286" count="1" type="stmt"/>
        <line num="287" count="0" type="stmt"/>
        <line num="290" count="0" type="stmt"/>
        <line num="293" count="0" type="stmt"/>
        <line num="294" count="0" type="stmt"/>
        <line num="296" count="0" type="stmt"/>
        <line num="301" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="302" count="0" type="stmt"/>
        <line num="307" count="1" type="stmt"/>
        <line num="308" count="0" type="stmt"/>
        <line num="311" count="0" type="stmt"/>
        <line num="314" count="0" type="stmt"/>
        <line num="335" count="0" type="stmt"/>
        <line num="336" count="0" type="stmt"/>
        <line num="340" count="1" type="stmt"/>
        <line num="341" count="0" type="stmt"/>
        <line num="343" count="0" type="stmt"/>
        <line num="345" count="0" type="stmt"/>
        <line num="347" count="0" type="stmt"/>
        <line num="348" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="349" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="351" count="0" type="stmt"/>
        <line num="352" count="0" type="stmt"/>
        <line num="353" count="0" type="stmt"/>
        <line num="355" count="0" type="stmt"/>
        <line num="356" count="0" type="stmt"/>
        <line num="358" count="0" type="stmt"/>
        <line num="359" count="0" type="stmt"/>
        <line num="364" count="0" type="stmt"/>
        <line num="365" count="0" type="stmt"/>
        <line num="370" count="1" type="stmt"/>
        <line num="376" count="0" type="stmt"/>
        <line num="378" count="0" type="stmt"/>
        <line num="381" count="0" type="stmt"/>
        <line num="382" count="0" type="stmt"/>
        <line num="383" count="0" type="stmt"/>
        <line num="384" count="0" type="stmt"/>
        <line num="386" count="0" type="stmt"/>
        <line num="395" count="1" type="stmt"/>
        <line num="400" count="0" type="stmt"/>
        <line num="402" count="0" type="stmt"/>
        <line num="404" count="0" type="stmt"/>
        <line num="413" count="0" type="stmt"/>
        <line num="414" count="0" type="stmt"/>
        <line num="419" count="1" type="stmt"/>
        <line num="423" count="0" type="stmt"/>
        <line num="426" count="0" type="stmt"/>
        <line num="429" count="0" type="stmt"/>
        <line num="430" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="431" count="0" type="stmt"/>
        <line num="432" count="0" type="stmt"/>
        <line num="435" count="0" type="stmt"/>
        <line num="438" count="0" type="stmt"/>
        <line num="440" count="0" type="stmt"/>
        <line num="443" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="444" count="0" type="stmt"/>
        <line num="445" count="0" type="stmt"/>
        <line num="448" count="0" type="stmt"/>
        <line num="450" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="451" count="0" type="stmt"/>
        <line num="453" count="0" type="stmt"/>
        <line num="457" count="1" type="stmt"/>
        <line num="462" count="0" type="stmt"/>
        <line num="464" count="0" type="stmt"/>
        <line num="467" count="0" type="stmt"/>
        <line num="469" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="470" count="0" type="stmt"/>
        <line num="471" count="0" type="stmt"/>
        <line num="478" count="0" type="stmt"/>
        <line num="481" count="0" type="stmt"/>
        <line num="483" count="0" type="stmt"/>
        <line num="486" count="0" type="stmt"/>
        <line num="488" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="489" count="0" type="stmt"/>
        <line num="490" count="0" type="stmt"/>
        <line num="499" count="0" type="stmt"/>
        <line num="501" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="502" count="0" type="stmt"/>
        <line num="503" count="0" type="stmt"/>
        <line num="504" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="505" count="0" type="stmt"/>
        <line num="506" count="0" type="stmt"/>
        <line num="508" count="0" type="stmt"/>
        <line num="509" count="0" type="stmt"/>
        <line num="512" count="0" type="stmt"/>
        <line num="513" count="0" type="stmt"/>
        <line num="515" count="0" type="stmt"/>
        <line num="517" count="0" type="stmt"/>
        <line num="521" count="1" type="stmt"/>
        <line num="522" count="0" type="stmt"/>
        <line num="524" count="0" type="stmt"/>
        <line num="527" count="0" type="stmt"/>
        <line num="529" count="0" type="stmt"/>
        <line num="532" count="0" type="stmt"/>
        <line num="534" count="0" type="stmt"/>
        <line num="537" count="0" type="stmt"/>
        <line num="539" count="0" type="stmt"/>
        <line num="542" count="0" type="stmt"/>
        <line num="548" count="0" type="stmt"/>
        <line num="549" count="0" type="stmt"/>
        <line num="551" count="0" type="stmt"/>
        <line num="553" count="0" type="stmt"/>
        <line num="558" count="1" type="stmt"/>
        <line num="559" count="0" type="stmt"/>
        <line num="562" count="0" type="stmt"/>
        <line num="563" count="0" type="stmt"/>
        <line num="566" count="0" type="stmt"/>
        <line num="567" count="0" type="stmt"/>
        <line num="569" count="0" type="stmt"/>
        <line num="574" count="0" type="cond" truecount="0" falsecount="5"/>
        <line num="576" count="0" type="stmt"/>
        <line num="578" count="0" type="stmt"/>
        <line num="580" count="0" type="stmt"/>
        <line num="582" count="0" type="stmt"/>
        <line num="584" count="0" type="stmt"/>
        <line num="590" count="1" type="stmt"/>
        <line num="591" count="0" type="stmt"/>
        <line num="592" count="0" type="stmt"/>
        <line num="595" count="0" type="stmt"/>
        <line num="601" count="0" type="stmt"/>
        <line num="607" count="0" type="stmt"/>
        <line num="608" count="0" type="stmt"/>
        <line num="612" count="1" type="stmt"/>
        <line num="613" count="0" type="stmt"/>
        <line num="614" count="0" type="stmt"/>
        <line num="617" count="0" type="stmt"/>
        <line num="622" count="0" type="stmt"/>
        <line num="628" count="0" type="stmt"/>
        <line num="629" count="0" type="stmt"/>
        <line num="633" count="1" type="stmt"/>
        <line num="634" count="0" type="stmt"/>
        <line num="635" count="0" type="stmt"/>
        <line num="637" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="638" count="0" type="stmt"/>
        <line num="639" count="0" type="stmt"/>
        <line num="643" count="0" type="stmt"/>
        <line num="648" count="0" type="stmt"/>
        <line num="654" count="0" type="stmt"/>
        <line num="655" count="0" type="stmt"/>
        <line num="659" count="1" type="stmt"/>
        <line num="660" count="0" type="stmt"/>
        <line num="661" count="0" type="stmt"/>
        <line num="663" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="664" count="0" type="stmt"/>
        <line num="665" count="0" type="stmt"/>
        <line num="669" count="0" type="stmt"/>
        <line num="670" count="0" type="stmt"/>
        <line num="673" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="674" count="0" type="stmt"/>
        <line num="675" count="0" type="stmt"/>
        <line num="679" count="0" type="stmt"/>
        <line num="680" count="0" type="stmt"/>
        <line num="681" count="0" type="stmt"/>
        <line num="682" count="0" type="stmt"/>
        <line num="683" count="0" type="stmt"/>
        <line num="684" count="0" type="stmt"/>
        <line num="687" count="0" type="stmt"/>
        <line num="690" count="0" type="stmt"/>
        <line num="697" count="0" type="stmt"/>
        <line num="698" count="0" type="stmt"/>
        <line num="702" count="1" type="stmt"/>
        <line num="703" count="0" type="stmt"/>
        <line num="704" count="0" type="stmt"/>
        <line num="706" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="707" count="0" type="stmt"/>
        <line num="708" count="0" type="stmt"/>
        <line num="712" count="0" type="stmt"/>
        <line num="713" count="0" type="stmt"/>
        <line num="715" count="0" type="stmt"/>
        <line num="720" count="0" type="stmt"/>
        <line num="721" count="0" type="stmt"/>
        <line num="726" count="1" type="stmt"/>
        <line num="727" count="0" type="stmt"/>
        <line num="735" count="1" type="stmt"/>
        <line num="738" count="1" type="stmt"/>
        <line num="739" count="0" type="stmt"/>
        <line num="740" count="0" type="stmt"/>
        <line num="743" count="0" type="stmt"/>
        <line num="744" count="0" type="stmt"/>
        <line num="747" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="748" count="0" type="stmt"/>
        <line num="749" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="750" count="0" type="stmt"/>
        <line num="754" count="0" type="stmt"/>
        <line num="758" count="1" type="cond" truecount="0" falsecount="1"/>
        <line num="759" count="0" type="stmt"/>
        <line num="760" count="0" type="stmt"/>
        <line num="766" count="1" type="stmt"/>
        <line num="771" count="1" type="stmt"/>
        <line num="773" count="1" type="cond" truecount="2" falsecount="0"/>
        <line num="774" count="1" type="stmt"/>
        <line num="775" count="0" type="stmt"/>
        <line num="776" count="0" type="stmt"/>
        <line num="780" count="1" type="stmt"/>
      </file>
      <file name="vite-env.d.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/vite-env.d.ts">
        <metrics statements="0" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="0" coveredmethods="0"/>
      </file>
    </package>
    <package name="src.components.ui">
      <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
      <file name="use-toast.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/components/ui/use-toast.ts">
        <metrics statements="2" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="2" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.hooks">
      <metrics statements="84" coveredstatements="0" conditionals="29" coveredconditionals="0" methods="32" coveredmethods="0"/>
      <file name="use-toast.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/hooks/use-toast.ts">
        <metrics statements="53" coveredstatements="0" conditionals="16" coveredconditionals="0" methods="18" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="9" count="0" type="stmt"/>
        <line num="18" count="0" type="stmt"/>
        <line num="25" count="0" type="stmt"/>
        <line num="28" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="stmt"/>
        <line num="59" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="60" count="0" type="stmt"/>
        <line num="63" count="0" type="stmt"/>
        <line num="64" count="0" type="stmt"/>
        <line num="65" count="0" type="stmt"/>
        <line num="71" count="0" type="stmt"/>
        <line num="74" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="77" count="0" type="stmt"/>
        <line num="83" count="0" type="stmt"/>
        <line num="86" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="91" count="0" type="stmt"/>
        <line num="95" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="96" count="0" type="stmt"/>
        <line num="98" count="0" type="stmt"/>
        <line num="99" count="0" type="stmt"/>
        <line num="103" count="0" type="stmt"/>
        <line num="106" count="0" type="cond" truecount="0" falsecount="4"/>
        <line num="116" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="117" count="0" type="stmt"/>
        <line num="122" count="0" type="stmt"/>
        <line num="124" count="0" type="stmt"/>
        <line num="129" count="0" type="stmt"/>
        <line num="131" count="0" type="stmt"/>
        <line num="134" count="0" type="stmt"/>
        <line num="135" count="0" type="stmt"/>
        <line num="136" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="stmt"/>
        <line num="146" count="0" type="stmt"/>
        <line num="150" count="0" type="stmt"/>
        <line num="152" count="0" type="stmt"/>
        <line num="159" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="164" count="0" type="stmt"/>
        <line num="172" count="0" type="stmt"/>
        <line num="174" count="0" type="stmt"/>
        <line num="175" count="0" type="stmt"/>
        <line num="176" count="0" type="stmt"/>
        <line num="177" count="0" type="stmt"/>
        <line num="178" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="179" count="0" type="stmt"/>
        <line num="184" count="0" type="stmt"/>
        <line num="187" count="0" type="stmt"/>
        <line num="191" count="0" type="stmt"/>
      </file>
      <file name="useComplianceApi.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/hooks/useComplianceApi.ts">
        <metrics statements="31" coveredstatements="0" conditionals="13" coveredconditionals="0" methods="14" coveredmethods="0"/>
        <line num="2" count="0" type="stmt"/>
        <line num="3" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="7" count="0" type="stmt"/>
        <line num="8" count="0" type="stmt"/>
        <line num="10" count="0" type="stmt"/>
        <line num="13" count="0" type="stmt"/>
        <line num="14" count="0" type="stmt"/>
        <line num="17" count="0" type="stmt"/>
        <line num="23" count="0" type="stmt"/>
        <line num="24" count="0" type="stmt"/>
        <line num="26" count="0" type="stmt"/>
        <line num="29" count="0" type="stmt"/>
        <line num="30" count="0" type="stmt"/>
        <line num="33" count="0" type="stmt"/>
        <line num="39" count="0" type="stmt"/>
        <line num="40" count="0" type="stmt"/>
        <line num="42" count="0" type="stmt"/>
        <line num="45" count="0" type="stmt"/>
        <line num="46" count="0" type="stmt"/>
        <line num="49" count="0" type="stmt"/>
        <line num="55" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="56" count="0" type="stmt"/>
        <line num="58" count="0" type="cond" truecount="0" falsecount="2"/>
        <line num="62" count="0" type="stmt"/>
        <line num="63" count="0" type="cond" truecount="0" falsecount="3"/>
        <line num="64" count="0" type="stmt"/>
        <line num="66" count="0" type="stmt"/>
        <line num="72" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="73" count="0" type="stmt"/>
        <line num="75" count="0" type="cond" truecount="0" falsecount="2"/>
      </file>
    </package>
    <package name="src.lib">
      <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
      <file name="utils.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/lib/utils.ts">
        <metrics statements="4" coveredstatements="0" conditionals="0" coveredconditionals="0" methods="1" coveredmethods="0"/>
        <line num="1" count="0" type="stmt"/>
        <line num="2" count="0" type="stmt"/>
        <line num="4" count="0" type="stmt"/>
        <line num="5" count="0" type="stmt"/>
      </file>
    </package>
    <package name="src.services">
      <metrics statements="22" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="5" coveredmethods="0"/>
      <file name="complianceApi.ts" path="/home/<USER>/Workspace/dbos/compliance-command-center-dbos/src/services/complianceApi.ts">
        <metrics statements="22" coveredstatements="0" conditionals="5" coveredconditionals="0" methods="5" coveredmethods="0"/>
        <line num="3" count="0" type="stmt"/>
        <line num="75" count="0" type="stmt"/>
        <line num="82" count="0" type="stmt"/>
        <line num="90" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="91" count="0" type="stmt"/>
        <line num="94" count="0" type="stmt"/>
        <line num="102" count="0" type="stmt"/>
        <line num="110" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="111" count="0" type="stmt"/>
        <line num="114" count="0" type="stmt"/>
        <line num="126" count="0" type="stmt"/>
        <line num="134" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="135" count="0" type="stmt"/>
        <line num="138" count="0" type="stmt"/>
        <line num="143" count="0" type="stmt"/>
        <line num="145" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="146" count="0" type="stmt"/>
        <line num="149" count="0" type="stmt"/>
        <line num="154" count="0" type="stmt"/>
        <line num="156" count="0" type="cond" truecount="0" falsecount="1"/>
        <line num="157" count="0" type="stmt"/>
        <line num="160" count="0" type="stmt"/>
      </file>
    </package>
  </project>
</coverage>
