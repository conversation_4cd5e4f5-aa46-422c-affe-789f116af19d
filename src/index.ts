import { DBOS, WorkflowQueue, ConfiguredInstance } from "@dbos-inc/dbos-sdk";
import express, { Request, Response, NextFunction } from "express";
import path from 'path';
import fs from 'fs';

// Types and Interfaces
interface ComplianceDocument {
  id: string;
  content: string;
  documentType: 'contract' | 'policy' | 'procedure' | 'financial_report';
  uploadedAt: Date;
  status: 'pending' | 'processing' | 'compliant' | 'non_compliant' | 'requires_review';
}

interface ComplianceRule {
  id: string;
  standard: 'SEC' | 'GLBA' | 'SOX' | 'GDPR' | 'CCPA';
  ruleType: 'data_protection' | 'financial_disclosure' | 'privacy' | 'security';
  description: string;
  pattern: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
}

interface KYCProfile {
  customerId: string;
  personalInfo: {
    name: string;
    dateOfBirth: string;
    ssn: string;
    address: string;
  };
  riskScore: number;
  status: 'pending' | 'approved' | 'rejected' | 'under_review';
  lastUpdated: Date;
}

interface ComplianceViolation {
  documentId: string;
  ruleId: string;
  violationType: string;
  description: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  recommendedAction: string;
  detectedAt: Date;
}

interface ComplianceReport {
  id: string;
  reportType: 'monthly' | 'quarterly' | 'annual' | 'incident';
  generatedAt: Date;
  compliance_rate: number;
  violations: ComplianceViolation[];
  recommendations: string[];
}

interface RegulatoryUpdate {
  id: string;
  standard: string;
  title: string;
  description: string;
  effectiveDate: Date;
  impact: 'low' | 'medium' | 'high';
  actionRequired: boolean;
}

// Express app setup
export const app = express();
app.use(express.json());

// Queues for different compliance processes
const complianceQueue = new WorkflowQueue("compliance_checks", { 
  concurrency: 5, 
  rateLimit: { limitPerPeriod: 100, periodSec: 60 } 
});

const kycQueue = new WorkflowQueue("kyc_processing", { 
  concurrency: 3,
  rateLimit: { limitPerPeriod: 50, periodSec: 60 }
});

const reportingQueue = new WorkflowQueue("report_generation", { 
  concurrency: 2 
});

// Mock compliance rules (in production, these would come from a database)
const COMPLIANCE_RULES: ComplianceRule[] = [
  {
    id: "SEC-001",
    standard: "SEC",
    ruleType: "financial_disclosure",
    description: "Financial statements must include quarterly earnings disclosure",
    pattern: "quarterly.*(earnings|revenue|income)",
    severity: "high"
  },
  {
    id: "GLBA-001",
    standard: "GLBA",
    ruleType: "privacy",
    description: "Customer financial information must be protected",
    pattern: "(ssn|social.security|account.number|routing.number)",
    severity: "critical"
  },
  {
    id: "SOX-001",
    standard: "SOX",
    ruleType: "financial_disclosure",
    description: "Internal controls must be documented",
    pattern: "internal.control.*documentation",
    severity: "high"
  }
];

export class ComplianceSystem {
  
  // Document Processing Steps
  @DBOS.step()
  static async validateDocument(document: ComplianceDocument): Promise<boolean> {
    DBOS.logger.info(`Validating document ${document.id}`);
    
    // Simulate document validation
    await DBOS.sleep(1000);
    
    // Check document format and completeness
    if (!document.content || document.content.length < 100) {
      DBOS.logger.warn(`Document ${document.id} failed validation - insufficient content`);
      return false;
    }
    
    DBOS.logger.info(`Document ${document.id} validation completed`);
    return true;
  }

  @DBOS.step()
  static async scanForViolations(document: ComplianceDocument): Promise<ComplianceViolation[]> {
    DBOS.logger.info(`Scanning document ${document.id} for compliance violations`);
    
    const violations: ComplianceViolation[] = [];
    
    // Simulate AI-powered compliance scanning
    await DBOS.sleep(2000);
    
    for (const rule of COMPLIANCE_RULES) {
      const regex = new RegExp(rule.pattern, 'gi');
      const matches = document.content.match(regex);
      
      if (matches) {
        // Check if this represents a violation based on context
        const isViolation = await ComplianceSystem.analyzeViolationContext(
          document.content, 
          rule, 
          matches
        );
        
        if (isViolation) {
          violations.push({
            documentId: document.id,
            ruleId: rule.id,
            violationType: rule.ruleType,
            description: `Potential ${rule.standard} violation: ${rule.description}`,
            severity: rule.severity,
            recommendedAction: ComplianceSystem.getRecommendedAction(rule),
            detectedAt: new Date()
          });
        }
      }
    }
    
    DBOS.logger.info(`Found ${violations.length} violations in document ${document.id}`);
    return violations;
  }

  @DBOS.step()
  static async analyzeViolationContext(
    content: string, 
    rule: ComplianceRule, 
    matches: string[]
  ): Promise<boolean> {
    // Simulate AI context analysis
    await DBOS.sleep(500);
    
    // Simple heuristic - in production, this would use ML models
    const contextWindow = 200;
    let hasViolation = false;
    
    for (const match of matches) {
      const matchIndex = content.indexOf(match);
      const context = content.substring(
        Math.max(0, matchIndex - contextWindow),
        Math.min(content.length, matchIndex + contextWindow + match.length)
      );
      
      // Check for protective measures or compliance statements
      const protectivePatterns = [
        'encrypted', 'protected', 'secure', 'compliant', 
        'privacy policy', 'data protection', 'authorized access'
      ];
      
      const hasProtection = protectivePatterns.some(pattern => 
        context.toLowerCase().includes(pattern)
      );
      
      if (!hasProtection && rule.severity === 'critical') {
        hasViolation = true;
        break;
      }
    }
    
    return hasViolation;
  }

  @DBOS.step()
  static async notifyComplianceTeam(violations: ComplianceViolation[]): Promise<void> {
    DBOS.logger.info(`Notifying compliance team of ${violations.length} violations`);
    
    // Simulate notification to compliance team
    await DBOS.sleep(500);
    
    const criticalViolations = violations.filter(v => v.severity === 'critical');
    const highViolations = violations.filter(v => v.severity === 'high');
    
    if (criticalViolations.length > 0) {
      DBOS.logger.warn(`CRITICAL: ${criticalViolations.length} critical violations detected`);
      // In production: send urgent notifications, create incidents
    }
    
    if (highViolations.length > 0) {
      DBOS.logger.warn(`HIGH: ${highViolations.length} high-severity violations detected`);
      // In production: send priority notifications
    }
    
    DBOS.logger.info('Compliance team notifications sent');
  }

  // KYC Processing Steps
  @DBOS.step()
  static async verifyIdentity(profile: KYCProfile): Promise<{ verified: boolean; confidence: number }> {
    DBOS.logger.info(`Verifying identity for customer ${profile.customerId}`);
    
    // Simulate identity verification via third-party services
    await DBOS.sleep(3000);
    
    // Mock verification logic
    const hasValidSSN = profile.personalInfo.ssn.length === 11; // XXX-XX-XXXX format
    const hasValidDOB = new Date(profile.personalInfo.dateOfBirth) < new Date();
    const hasValidAddress = profile.personalInfo.address.length > 10;
    
    const confidence = (hasValidSSN ? 0.4 : 0) + 
                      (hasValidDOB ? 0.3 : 0) + 
                      (hasValidAddress ? 0.3 : 0);
    
    const verified = confidence >= 0.8;
    
    DBOS.logger.info(`Identity verification completed: ${verified ? 'PASSED' : 'FAILED'} (${confidence})`);
    return { verified, confidence };
  }

  @DBOS.step()
  static async performRiskAssessment(profile: KYCProfile): Promise<number> {
    DBOS.logger.info(`Performing risk assessment for customer ${profile.customerId}`);
    
    // Simulate risk assessment
    await DBOS.sleep(2000);
    
    let riskScore = 0;
    
    // Age-based risk (younger = higher risk)
    const age = new Date().getFullYear() - new Date(profile.personalInfo.dateOfBirth).getFullYear();
    if (age < 25) riskScore += 20;
    else if (age < 35) riskScore += 10;
    
    // Address-based risk (simplified)
    const highRiskZipPrefixes = ['900', '800', '700']; // Mock high-risk areas
    const zipCode = profile.personalInfo.address.match(/\d{5}/)?.[0];
    if (zipCode && highRiskZipPrefixes.some(prefix => zipCode.startsWith(prefix))) {
      riskScore += 30;
    }
    
    // Random factor for demonstration
    riskScore += Math.floor(Math.random() * 20);
    
    DBOS.logger.info(`Risk assessment completed: score ${riskScore}`);
    return Math.min(riskScore, 100);
  }

  @DBOS.step()
  static async checkSanctionsList(profile: KYCProfile): Promise<{ isListed: boolean; details?: string }> {
    DBOS.logger.info(`Checking sanctions list for customer ${profile.customerId}`);
    
    // Simulate sanctions list check
    await DBOS.sleep(1500);
    
    // Mock sanctions check - in production, this would query OFAC, UN, etc.
    const sanctionedNames = ['John Doe', 'Jane Smith']; // Mock list
    const isListed = sanctionedNames.includes(profile.personalInfo.name);
    
    const result = {
      isListed,
      details: isListed ? 'Found match in OFAC sanctions list' : undefined
    };
    
    DBOS.logger.info(`Sanctions check completed: ${isListed ? 'MATCH FOUND' : 'CLEAR'}`);
    return result;
  }

  // Regulatory Monitoring Steps
  @DBOS.step()
  static async fetchRegulatoryUpdates(): Promise<RegulatoryUpdate[]> {
    DBOS.logger.info('Fetching latest regulatory updates');
    
    // Simulate fetching from regulatory websites/APIs
    await DBOS.sleep(2000);
    
    // Mock regulatory updates
    const updates: RegulatoryUpdate[] = [
      {
        id: 'SEC-2024-001',
        standard: 'SEC',
        title: 'Updated Cybersecurity Disclosure Requirements',
        description: 'New requirements for cybersecurity incident reporting within 4 business days',
        effectiveDate: new Date('2024-12-01'),
        impact: 'high',
        actionRequired: true
      },
      {
        id: 'GLBA-2024-002',
        standard: 'GLBA',
        title: 'Enhanced Privacy Notice Requirements',
        description: 'Updated privacy notice requirements for financial institutions',
        effectiveDate: new Date('2024-11-15'),
        impact: 'medium',
        actionRequired: true
      }
    ];
    
    DBOS.logger.info(`Fetched ${updates.length} regulatory updates`);
    return updates;
  }

  @DBOS.step()
  static async analyzeRegulatoryImpact(updates: RegulatoryUpdate[]): Promise<string[]> {
    DBOS.logger.info('Analyzing regulatory impact');
    
    await DBOS.sleep(1000);
    
    const recommendations: string[] = [];
    
    for (const update of updates) {
      if (update.actionRequired) {
        switch (update.impact) {
          case 'high':
            recommendations.push(`URGENT: Review and update policies for ${update.title}`);
            recommendations.push(`URGENT: Train compliance team on ${update.standard} changes`);
            break;
          case 'medium':
            recommendations.push(`PRIORITY: Update procedures for ${update.title}`);
            break;
          case 'low':
            recommendations.push(`MONITOR: Track implementation of ${update.title}`);
            break;
        }
      }
    }
    
    DBOS.logger.info(`Generated ${recommendations.length} recommendations`);
    return recommendations;
  }

  // Report Generation Steps
  @DBOS.step()
  static async generateComplianceMetrics(): Promise<{
    totalDocuments: number;
    compliantDocuments: number;
    violationsCount: number;
    complianceRate: number;
  }> {
    DBOS.logger.info('Generating compliance metrics');
    
    await DBOS.sleep(1000);
    
    // Mock metrics - in production, query from database
    const totalDocuments = 1000;
    const compliantDocuments = 850;
    const violationsCount = 150;
    const complianceRate = (compliantDocuments / totalDocuments) * 100;
    
    return {
      totalDocuments,
      compliantDocuments,
      violationsCount,
      complianceRate
    };
  }

  @DBOS.step()
  static async formatComplianceReport(
    metrics: any, 
    violations: ComplianceViolation[], 
    recommendations: string[]
  ): Promise<ComplianceReport> {
    DBOS.logger.info('Formatting compliance report');
    
    await DBOS.sleep(500);
    
    const report: ComplianceReport = {
      id: `RPT-${Date.now()}`,
      reportType: 'monthly',
      generatedAt: new Date(),
      compliance_rate: metrics.complianceRate,
      violations: violations.slice(0, 10), // Top 10 violations
      recommendations
    };
    
    DBOS.logger.info(`Compliance report ${report.id} formatted`);
    return report;
  }

  // Workflow Orchestration
  @DBOS.workflow()
  static async processComplianceDocument(document: ComplianceDocument): Promise<{
    status: string;
    violations: ComplianceViolation[];
  }> {
    DBOS.logger.info(`Starting compliance processing for document ${document.id}`);
    
    // Emit processing status
    await DBOS.setEvent('processing_status', 'started');
    
    // Step 1: Validate document
    const isValid = await ComplianceSystem.validateDocument(document);
    if (!isValid) {
      await DBOS.setEvent('processing_status', 'failed_validation');
      return { status: 'invalid', violations: [] };
    }
    
    await DBOS.setEvent('processing_status', 'validation_passed');
    
    // Step 2: Scan for violations
    const violations = await ComplianceSystem.scanForViolations(document);
    
    await DBOS.setEvent('violations_found', violations.length);
    
    // Step 3: Notify compliance team if violations found
    if (violations.length > 0) {
      await ComplianceSystem.notifyComplianceTeam(violations);
      await DBOS.setEvent('processing_status', 'violations_reported');
    }
    
    await DBOS.setEvent('processing_status', 'completed');
    
    const status = violations.length > 0 ? 'non_compliant' : 'compliant';
    DBOS.logger.info(`Compliance processing completed for document ${document.id}: ${status}`);
    
    return { status, violations };
  }

  @DBOS.workflow()
  static async processKYCCustomer(profile: KYCProfile): Promise<{
    status: 'approved' | 'rejected' | 'under_review';
    riskScore: number;
    reasons: string[];
  }> {
    DBOS.logger.info(`Starting KYC processing for customer ${profile.customerId}`);
    
    await DBOS.setEvent('kyc_status', 'identity_verification');
    
    // Step 1: Identity verification
    const identityResult = await ComplianceSystem.verifyIdentity(profile);
    
    if (!identityResult.verified) {
      await DBOS.setEvent('kyc_status', 'identity_failed');
      return {
        status: 'rejected',
        riskScore: 100,
        reasons: ['Identity verification failed']
      };
    }
    
    await DBOS.setEvent('kyc_status', 'risk_assessment');
    
    // Step 2: Risk assessment
    const riskScore = await ComplianceSystem.performRiskAssessment(profile);
    
    await DBOS.setEvent('kyc_status', 'sanctions_check');
    
    // Step 3: Sanctions list check
    const sanctionsResult = await ComplianceSystem.checkSanctionsList(profile);
    
    if (sanctionsResult.isListed) {
      await DBOS.setEvent('kyc_status', 'sanctions_match');
      return {
        status: 'rejected',
        riskScore: 100,
        reasons: [`Sanctions list match: ${sanctionsResult.details}`]
      };
    }
    
    // Determine final status
    let status: 'approved' | 'rejected' | 'under_review';
    const reasons: string[] = [];
    
    if (riskScore >= 70) {
      status = 'under_review';
      reasons.push('High risk score requires manual review');
    } else if (riskScore >= 50) {
      status = 'under_review';
      reasons.push('Medium risk score requires additional verification');
    } else {
      status = 'approved';
      reasons.push('Low risk profile - automatically approved');
    }
    
    await DBOS.setEvent('kyc_status', 'completed');
    await DBOS.setEvent('final_status', status);
    
    DBOS.logger.info(`KYC processing completed for customer ${profile.customerId}: ${status}`);
    
    return { status, riskScore, reasons };
  }

  @DBOS.workflow()
  static async generateComplianceReport(reportType: 'monthly' | 'quarterly' | 'annual'): Promise<ComplianceReport> {
    DBOS.logger.info(`Generating ${reportType} compliance report`);
    
    await DBOS.setEvent('report_status', 'metrics_generation');
    
    // Step 1: Generate metrics
    const metrics = await ComplianceSystem.generateComplianceMetrics();
    
    await DBOS.setEvent('report_status', 'regulatory_updates');
    
    // Step 2: Fetch regulatory updates
    const regulatoryUpdates = await ComplianceSystem.fetchRegulatoryUpdates();
    
    await DBOS.setEvent('report_status', 'impact_analysis');
    
    // Step 3: Analyze impact
    const recommendations = await ComplianceSystem.analyzeRegulatoryImpact(regulatoryUpdates);
    
    await DBOS.setEvent('report_status', 'formatting');
    
    // Step 4: Format report
    const report = await ComplianceSystem.formatComplianceReport(
      metrics, 
      [], // Mock violations for report
      recommendations
    );
    
    await DBOS.setEvent('report_status', 'completed');
    await DBOS.setEvent('report_id', report.id);
    
    DBOS.logger.info(`Compliance report ${report.id} generated successfully`);
    
    return report;
  }

  @DBOS.scheduled({ crontab: "0 9 * * 1" }) // Every Monday at 9 AM
  @DBOS.workflow()
  static async weeklyRegulatoryMonitoring(scheduledTime: Date, startTime: Date): Promise<void> {
    DBOS.logger.info(`Starting weekly regulatory monitoring at ${scheduledTime}`);
    
    // Fetch and analyze regulatory updates
    const updates = await ComplianceSystem.fetchRegulatoryUpdates();
    const recommendations = await ComplianceSystem.analyzeRegulatoryImpact(updates);
    
    // Emit findings for monitoring
    await DBOS.setEvent('weekly_updates_count', updates.length);
    await DBOS.setEvent('weekly_recommendations', recommendations);
    
    DBOS.logger.info(`Weekly regulatory monitoring completed - ${updates.length} updates processed`);
  }

  // Utility methods
  static getRecommendedAction(rule: ComplianceRule): string {
    switch (rule.severity) {
      case 'critical':
        return 'Immediate remediation required - escalate to legal team';
      case 'high':
        return 'Priority remediation - update within 24 hours';
      case 'medium':
        return 'Schedule remediation within 1 week';
      case 'low':
        return 'Monitor and address in next review cycle';
      default:
        return 'Review and assess appropriate action';
    }
  }
}

// API Endpoints
app.post('/api/compliance/document', async (req: Request, res: Response): Promise<void> => {
  try {
    const document: ComplianceDocument = req.body;

    // Start compliance processing workflow
    const handle = await DBOS.startWorkflow(
      ComplianceSystem,
      { queueName: complianceQueue.name }
    ).processComplianceDocument(document);

    // Return workflow ID for tracking
    res.json({
      workflowId: handle.workflowID,
      status: 'processing_started',
      message: 'Document compliance check initiated'
    });
  } catch (error) {
    DBOS.logger.error(`Error processing document: ${(error as Error).message}`);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/kyc/customer', async (req: Request, res: Response): Promise<void> => {
  try {
    const profile: KYCProfile = req.body;

    // Start KYC processing workflow
    const handle = await DBOS.startWorkflow(
      ComplianceSystem,
      { queueName: kycQueue.name }
    ).processKYCCustomer(profile);

    res.json({
      workflowId: handle.workflowID,
      status: 'kyc_processing_started',
      message: 'KYC verification initiated'
    });
  } catch (error) {
    DBOS.logger.error(`Error processing KYC: ${(error as Error).message}`);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.post('/api/reports/generate', async (req: Request, res: Response): Promise<void> => {
  try {
    const { reportType } = req.body;

    if (!['monthly', 'quarterly', 'annual'].includes(reportType)) {
      res.status(400).json({ error: 'Invalid report type' });
      return;
    }

    // Start report generation workflow
    const handle = await DBOS.startWorkflow(
      ComplianceSystem,
      { queueName: reportingQueue.name }
    ).generateComplianceReport(reportType);

    res.json({
      workflowId: handle.workflowID,
      status: 'report_generation_started',
      message: `${reportType} compliance report generation initiated`
    });
  } catch (error) {
    DBOS.logger.error(`Error generating report: ${(error as Error).message}`);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.get('/api/workflow/:workflowId/status', async (req: Request, res: Response): Promise<void> => {
  try {
    const { workflowId } = req.params;

    if (!workflowId) {
      res.status(400).json({ error: 'Workflow ID is required' });
      return;
    }

    // Retrieve workflow handle and get status
    const handle = await DBOS.retrieveWorkflow(workflowId);
    const status = await handle.getStatus();

    // Check if status is null
    if (!status) {
      res.status(404).json({ error: 'Workflow status not available' });
      return;
    }

    // Get workflow events for detailed progress
    const events: Record<string, any> = {};
    try {
      events['processing_status'] = await DBOS.getEvent(workflowId, 'processing_status', 1);
      events['violations_found'] = await DBOS.getEvent(workflowId, 'violations_found', 1);
      events['kyc_status'] = await DBOS.getEvent(workflowId, 'kyc_status', 1);
      events['report_status'] = await DBOS.getEvent(workflowId, 'report_status', 1);
    } catch (eventError) {
      // Events might not exist yet
      DBOS.logger.info(`No events found for workflow ${workflowId}`);
    }

    res.json({
      workflowId,
      status: status.status,
      workflowName: status.workflowName,
      events
    });
  } catch (error) {
    DBOS.logger.error(`Error getting workflow status: ${(error as Error).message}`);
    res.status(404).json({ error: 'Workflow not found' });
  }
});

app.get('/api/workflow/:workflowId/result', async (req: Request, res: Response): Promise<void> => {
  try {
    const { workflowId } = req.params;

    if (!workflowId) {
      res.status(400).json({ error: 'Workflow ID is required' });
      return;
    }

    // Retrieve workflow handle and get result
    const handle = await DBOS.retrieveWorkflow(workflowId);
    const result = await handle.getResult();

    res.json({
      workflowId,
      result
    });
  } catch (error) {
    DBOS.logger.error(`Error getting workflow result: ${(error as Error).message}`);
    res.status(404).json({ error: 'Workflow not found or not completed' });
  }
});

// Health check endpoint
app.get('/health', (req: Request, res: Response): void => {
  res.json({
    status: 'healthy',
    service: 'regulatory-compliance-system',
    timestamp: new Date().toISOString()
  });
});

// Serve static files from dist directory
app.use(express.static(path.resolve('dist')));

// Handle static assets with specific extensions
app.get(/\.(js|css|svg|txt|ico|png|jpg|jpeg|gif|woff|woff2|ttf|eot)$/, (req: Request, res: Response): void => {
  const requestPath = req.path;
  console.log(`Static file request: ${requestPath}`);
  if(requestPath === '/') {
    console.log('Serving index.html');
    res.sendFile(path.resolve('dist', 'index.html'));
    return;
  }

  // Try to serve from dist/assets first, then from dist root
  const assetsPath = path.resolve('dist', 'assets', path.basename(requestPath));
  const rootPath = path.resolve('dist', requestPath.substring(1));

  // Check if file exists in assets directory first
  if (fs.existsSync(assetsPath)) {
    return res.sendFile(assetsPath);
  } else if (fs.existsSync(rootPath)) {
    return res.sendFile(rootPath);
  }

  // If file not found, return 404
  res.status(404).send('File not found');
});

// SPA fallback - serve index.html for all other routes (only in production)
/*if (process.env.NODE_ENV !== 'test') {
  app.get('*', (_req: Request, res: Response): void => {
    res.sendFile(path.resolve('dist', 'index.html'));
  });
}*/

// Main function
async function main() {
  DBOS.setConfig({
    name: "regulatory-compliance-system",
    databaseUrl: process.env.DBOS_DATABASE_URL
  });
  
  await DBOS.launch({ expressApp: app });
  
  const PORT = process.env.PORT || 3000;
  app.listen(PORT, () => {
    console.log(`🏛️  Regulatory Compliance System running on http://localhost:${PORT}`);
    console.log(`📊 Compliance checking, KYC processing, and regulatory monitoring active`);
  });
}

main().catch(console.log);
