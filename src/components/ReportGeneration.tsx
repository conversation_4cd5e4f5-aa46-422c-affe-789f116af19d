
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { FileText, Download, Clock, CheckCircle, Calendar, BarChart } from 'lucide-react';
import { toast } from 'sonner';

const ReportGeneration = () => {
  const [selectedReportType, setSelectedReportType] = useState('');
  const [selectedPeriod, setSelectedPeriod] = useState('');
  const [generating, setGenerating] = useState(false);

  const reportTypes = [
    {
      id: 'compliance-summary',
      name: 'Compliance Summary Report',
      description: 'Executive overview of compliance status across all standards',
      frequency: 'Monthly, Quarterly, Annual',
      stakeholders: 'Board, Executives, Regulators'
    },
    {
      id: 'kyc-status',
      name: 'KYC Status Report',
      description: 'Customer verification status and processing metrics',
      frequency: 'Weekly, Monthly',
      stakeholders: 'Operations, Compliance Team'
    },
    {
      id: 'violations-analysis',
      name: 'Violations Analysis Report',
      description: 'Detailed analysis of compliance violations and remediation',
      frequency: 'Monthly, Quarterly',
      stakeholders: 'Compliance Team, Legal'
    },
    {
      id: 'regulatory-impact',
      name: 'Regulatory Impact Assessment',
      description: 'Analysis of recent regulatory changes and required actions',
      frequency: 'Quarterly, Annual',
      stakeholders: 'Executives, Legal, Operations'
    },
    {
      id: 'audit-trail',
      name: 'Audit Trail Report',
      description: 'Complete workflow history for regulatory examinations',
      frequency: 'On-demand',
      stakeholders: 'Auditors, Regulators'
    }
  ];

  const recentReports = [
    {
      id: 'RPT-2024-045',
      name: 'Q2 2024 Compliance Summary',
      type: 'Compliance Summary',
      generatedDate: '2024-06-09 09:30',
      status: 'Completed',
      size: '2.4 MB',
      pages: 15,
      recipients: ['Board', 'Executive Team']
    },
    {
      id: 'RPT-2024-044',
      name: 'June KYC Status Report',
      type: 'KYC Status',
      generatedDate: '2024-06-08 16:45',
      status: 'Completed',
      size: '1.8 MB',
      pages: 8,
      recipients: ['Operations', 'Compliance']
    },
    {
      id: 'RPT-2024-043',
      name: 'May Violations Analysis',
      type: 'Violations Analysis',
      generatedDate: '2024-06-07 14:20',
      status: 'Generating',
      size: 'Pending',
      pages: 'Pending',
      recipients: ['Compliance Team']
    }
  ];

  const reportStats = {
    totalGenerated: 245,
    averageTime: '45 seconds',
    automationRate: 100,
    pagesSaved: '12,500+'
  };

  const handleGenerateReport = async () => {
    if (!selectedReportType || !selectedPeriod) {
      toast.error('Please select report type and period');
      return;
    }

    setGenerating(true);
    
    // Simulate report generation
    setTimeout(() => {
      setGenerating(false);
      toast.success('Report generated successfully! Download link sent to your email.');
    }, 3000);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Generating': return 'bg-blue-100 text-blue-800';
      case 'Failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Report Generation Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Reports Generated</p>
                <p className="text-2xl font-bold text-blue-600">{reportStats.totalGenerated}</p>
              </div>
              <FileText className="w-8 h-8 text-blue-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">This year</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Generation Time</p>
                <p className="text-2xl font-bold text-green-600">{reportStats.averageTime}</p>
              </div>
              <Clock className="w-8 h-8 text-green-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">95% faster than manual</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Automation Rate</p>
                <p className="text-2xl font-bold text-purple-600">{reportStats.automationRate}%</p>
              </div>
              <BarChart className="w-8 h-8 text-purple-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">Fully automated</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pages Generated</p>
                <p className="text-2xl font-bold text-orange-600">{reportStats.pagesSaved}</p>
              </div>
              <FileText className="w-8 h-8 text-orange-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">Cost savings</p>
          </CardContent>
        </Card>
      </div>

      {/* Report Generation Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="w-5 h-5 mr-2" />
            Generate Compliance Report
          </CardTitle>
          <CardDescription>
            Automated report generation with real-time data and AI insights
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <Label htmlFor="reportType">Report Type</Label>
              <Select value={selectedReportType} onValueChange={setSelectedReportType}>
                <SelectTrigger>
                  <SelectValue placeholder="Select report type" />
                </SelectTrigger>
                <SelectContent>
                  {reportTypes.map((type) => (
                    <SelectItem key={type.id} value={type.id}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label htmlFor="period">Reporting Period</Label>
              <Select value={selectedPeriod} onValueChange={setSelectedPeriod}>
                <SelectTrigger>
                  <SelectValue placeholder="Select period" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="current-month">Current Month</SelectItem>
                  <SelectItem value="last-month">Last Month</SelectItem>
                  <SelectItem value="current-quarter">Current Quarter</SelectItem>
                  <SelectItem value="last-quarter">Last Quarter</SelectItem>
                  <SelectItem value="current-year">Current Year</SelectItem>
                  <SelectItem value="custom">Custom Range</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-end">
              <Button 
                onClick={handleGenerateReport} 
                disabled={generating}
                className="w-full"
              >
                {generating ? (
                  <>
                    <Clock className="w-4 h-4 mr-2 animate-spin" />
                    Generating...
                  </>
                ) : (
                  <>
                    <FileText className="w-4 h-4 mr-2" />
                    Generate Report
                  </>
                )}
              </Button>
            </div>
          </div>

          {selectedReportType && (
            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              {reportTypes.find(type => type.id === selectedReportType) && (
                <div>
                  <h4 className="font-medium text-blue-900 mb-2">
                    {reportTypes.find(type => type.id === selectedReportType)?.name}
                  </h4>
                  <p className="text-sm text-blue-700 mb-2">
                    {reportTypes.find(type => type.id === selectedReportType)?.description}
                  </p>
                  <div className="grid grid-cols-2 gap-4 text-xs">
                    <div>
                      <span className="font-medium">Frequency:</span>{' '}
                      {reportTypes.find(type => type.id === selectedReportType)?.frequency}
                    </div>
                    <div>
                      <span className="font-medium">Stakeholders:</span>{' '}
                      {reportTypes.find(type => type.id === selectedReportType)?.stakeholders}
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Recent Reports */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Reports</CardTitle>
          <CardDescription>
            Generated compliance reports and their status
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentReports.map((report) => (
              <div key={report.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-4">
                  <FileText className="w-8 h-8 text-blue-500" />
                  <div>
                    <h4 className="font-medium">{report.name}</h4>
                    <div className="flex items-center space-x-2 mt-1">
                      <Badge variant="outline">{report.id}</Badge>
                      <Badge variant="outline">{report.type}</Badge>
                    </div>
                    <div className="flex items-center space-x-4 text-sm text-gray-600 mt-1">
                      <span>Generated: {report.generatedDate}</span>
                      {report.status === 'Completed' && (
                        <>
                          <span>•</span>
                          <span>{report.size} ({report.pages} pages)</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <Badge className={getStatusColor(report.status)}>
                      {report.status === 'Completed' && <CheckCircle className="w-3 h-3 mr-1" />}
                      {report.status === 'Generating' && <Clock className="w-3 h-3 mr-1" />}
                      {report.status}
                    </Badge>
                    <p className="text-xs text-gray-500 mt-1">
                      Recipients: {report.recipients.join(', ')}
                    </p>
                  </div>
                  {report.status === 'Completed' && (
                    <Button variant="outline" size="sm">
                      <Download className="w-4 h-4 mr-1" />
                      Download
                    </Button>
                  )}
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Report Templates */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Automated Features</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <div>
                  <p className="font-medium">Real-time Data Integration</p>
                  <p className="text-sm text-gray-600">Live compliance metrics and violation status</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <div>
                  <p className="font-medium">AI-Generated Insights</p>
                  <p className="text-sm text-gray-600">Trend analysis and recommendations</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <div>
                  <p className="font-medium">Executive Summaries</p>
                  <p className="text-sm text-gray-600">Key findings and action items</p>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="w-5 h-5 text-green-500" />
                <div>
                  <p className="font-medium">Audit Trail Documentation</p>
                  <p className="text-sm text-gray-600">Complete workflow history</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Report Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-1">Automated Delivery</h4>
                <p className="text-sm text-blue-700">Reports sent automatically to stakeholders</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-1">Multiple Formats</h4>
                <p className="text-sm text-green-700">PDF, Excel, PowerPoint formats available</p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <h4 className="font-medium text-purple-900 mb-1">Secure Access</h4>
                <p className="text-sm text-purple-700">Role-based access and encryption</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <h4 className="font-medium text-orange-900 mb-1">Scheduled Generation</h4>
                <p className="text-sm text-orange-700">Automatic report creation on schedule</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ReportGeneration;
