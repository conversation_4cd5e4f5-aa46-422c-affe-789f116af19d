
import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { AlertTriangle, CheckCircle, Clock, Eye, FileText } from 'lucide-react';

const ComplianceOverview = () => {
  const recentViolations = [
    {
      id: 1,
      document: 'Q3_Financial_Report.pdf',
      violation: 'Missing SOX disclosure statement',
      severity: 'Critical',
      date: '2024-06-08',
      status: 'Under Review'
    },
    {
      id: 2,
      document: 'Customer_Agreement_v2.pdf',
      violation: 'GLBA privacy notice incomplete',
      severity: 'High',
      date: '2024-06-07',
      status: 'Remediation Required'
    },
    {
      id: 3,
      document: 'Risk_Assessment_May.pdf',
      violation: 'SEC reporting format non-compliance',
      severity: 'Medium',
      date: '2024-06-06',
      status: 'In Progress'
    }
  ];

  const complianceStandards = [
    { name: 'SEC Regulations', compliance: 98, violations: 1, lastCheck: '2024-06-09' },
    { name: 'GLBA Requirements', compliance: 95, violations: 2, lastCheck: '2024-06-09' },
    { name: 'SOX Compliance', compliance: 99, violations: 0, lastCheck: '2024-06-08' },
    { name: 'FINRA Rules', compliance: 97, violations: 0, lastCheck: '2024-06-08' }
  ];

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case 'Critical': return 'bg-red-100 text-red-800';
      case 'High': return 'bg-orange-100 text-orange-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <div className="space-y-6">
      {/* Compliance Standards Overview */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <CheckCircle className="w-5 h-5 mr-2 text-green-500" />
            Compliance Standards Status
          </CardTitle>
          <CardDescription>
            Real-time compliance rates across all regulatory standards
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            {complianceStandards.map((standard, index) => (
              <div key={index} className="p-4 border rounded-lg">
                <h4 className="font-semibold text-sm mb-2">{standard.name}</h4>
                <div className="flex items-center justify-between mb-2">
                  <span className="text-2xl font-bold text-blue-600">{standard.compliance}%</span>
                  {standard.violations > 0 ? (
                    <Badge variant="destructive">{standard.violations} issues</Badge>
                  ) : (
                    <Badge variant="default" className="bg-green-100 text-green-800">✓ Compliant</Badge>
                  )}
                </div>
                <p className="text-xs text-gray-500">Last checked: {standard.lastCheck}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Recent Violations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <AlertTriangle className="w-5 h-5 mr-2 text-red-500" />
            Recent Compliance Violations
          </CardTitle>
          <CardDescription>
            AI-detected violations requiring immediate attention
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {recentViolations.map((violation) => (
              <div key={violation.id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-1">
                    <FileText className="w-4 h-4 text-gray-500" />
                    <span className="font-medium">{violation.document}</span>
                    <Badge className={getSeverityColor(violation.severity)}>
                      {violation.severity}
                    </Badge>
                  </div>
                  <p className="text-sm text-gray-600 mb-1">{violation.violation}</p>
                  <div className="flex items-center space-x-4 text-xs text-gray-500">
                    <span>Detected: {violation.date}</span>
                    <span>Status: {violation.status}</span>
                  </div>
                </div>
                <div className="flex space-x-2">
                  <Button variant="outline" size="sm">
                    <Eye className="w-4 h-4 mr-1" />
                    Review
                  </Button>
                  <Button variant="default" size="sm">
                    Remediate
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* AI Insights */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>AI Compliance Insights</CardTitle>
            <CardDescription>Automated analysis and recommendations</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-1">Pattern Detection</h4>
                <p className="text-sm text-blue-700">SOX disclosure statements missing in 60% of Q3 documents</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-1">Improvement Trend</h4>
                <p className="text-sm text-green-700">GLBA compliance improved 15% this quarter</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <h4 className="font-medium text-orange-900 mb-1">Risk Alert</h4>
                <p className="text-sm text-orange-700">New SEC rule may impact 23 existing documents</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Workflow Performance</CardTitle>
            <CardDescription>DBOS-powered automation metrics</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Document Processing</span>
                <span className="text-lg font-bold text-green-600">2.3s avg</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">KYC Completion Rate</span>
                <span className="text-lg font-bold text-blue-600">94%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Zero Downtime</span>
                <span className="text-lg font-bold text-purple-600">99.9%</span>
              </div>
              <div className="flex justify-between items-center">
                <span className="text-sm font-medium">Cost Savings</span>
                <span className="text-lg font-bold text-green-600">$2.4M</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default ComplianceOverview;
