
import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Progress } from '@/components/ui/progress';
import { Search, Activity, Clock, CheckCircle, AlertTriangle, Play, Pause, RotateCcw } from 'lucide-react';

const WorkflowStatus = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const activeWorkflows = [
    {
      id: 'WF-COMP-2024-156',
      type: 'Compliance Check',
      document: 'Q3_Financial_Report.pdf',
      status: 'Running',
      progress: 75,
      currentStep: 'AI Violation Detection',
      totalSteps: 4,
      startTime: '2024-06-09 14:30:00',
      estimatedCompletion: '2024-06-09 14:35:00',
      executor: 'DBOS-Worker-01'
    },
    {
      id: 'WF-KYC-2024-089',
      type: 'KYC Process',
      document: 'Customer: <PERSON>',
      status: 'Running',
      progress: 50,
      currentStep: 'Sanctions Screening',
      totalSteps: 4,
      startTime: '2024-06-09 14:15:00',
      estimatedCompletion: '2024-06-09 14:45:00',
      executor: 'DBOS-Worker-02'
    },
    {
      id: 'WF-REP-2024-034',
      type: 'Report Generation',
      document: 'Monthly Compliance Summary',
      status: 'Paused',
      progress: 25,
      currentStep: 'Data Collection',
      totalSteps: 5,
      startTime: '2024-06-09 13:45:00',
      estimatedCompletion: 'Paused',
      executor: 'DBOS-Worker-03'
    },
    {
      id: 'WF-MON-2024-012',
      type: 'Regulatory Monitoring',
      document: 'SEC Rule Updates Check',
      status: 'Completed',
      progress: 100,
      currentStep: 'Completed',
      totalSteps: 3,
      startTime: '2024-06-09 14:00:00',
      estimatedCompletion: '2024-06-09 14:05:00',
      executor: 'DBOS-Worker-04'
    }
  ];

  const workflowStats = {
    totalExecuted: 2456,
    currentlyRunning: 15,
    avgExecutionTime: '3.2 min',
    successRate: 99.7
  };

  const dbosMetrics = [
    { metric: 'Workflow Durability', value: '100%', description: 'Zero workflow data loss' },
    { metric: 'Fault Recovery', value: '< 1s', description: 'Average recovery time' },
    { metric: 'Concurrency Control', value: '50+', description: 'Parallel workflows' },
    { metric: 'Resource Utilization', value: '85%', description: 'Optimal efficiency' }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Running': return 'bg-blue-100 text-blue-800';
      case 'Completed': return 'bg-green-100 text-green-800';
      case 'Paused': return 'bg-yellow-100 text-yellow-800';
      case 'Failed': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'Running': return <Activity className="w-4 h-4 animate-pulse" />;
      case 'Completed': return <CheckCircle className="w-4 h-4" />;
      case 'Paused': return <Pause className="w-4 h-4" />;
      case 'Failed': return <AlertTriangle className="w-4 h-4" />;
      default: return <Clock className="w-4 h-4" />;
    }
  };

  const filteredWorkflows = activeWorkflows.filter(workflow =>
    workflow.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
    workflow.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
    workflow.document.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="space-y-6">
      {/* Workflow Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Executed</p>
                <p className="text-2xl font-bold text-blue-600">{workflowStats.totalExecuted}</p>
              </div>
              <Activity className="w-8 h-8 text-blue-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">This quarter</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Currently Running</p>
                <p className="text-2xl font-bold text-green-600">{workflowStats.currentlyRunning}</p>
              </div>
              <Play className="w-8 h-8 text-green-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">Active workflows</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Execution</p>
                <p className="text-2xl font-bold text-purple-600">{workflowStats.avgExecutionTime}</p>
              </div>
              <Clock className="w-8 h-8 text-purple-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">Processing time</p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Success Rate</p>
                <p className="text-2xl font-bold text-orange-600">{workflowStats.successRate}%</p>
              </div>
              <CheckCircle className="w-8 h-8 text-orange-500" />
            </div>
            <p className="text-xs text-gray-500 mt-1">Reliability score</p>
          </CardContent>
        </Card>
      </div>

      {/* Active Workflows */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="flex items-center">
                <Activity className="w-5 h-5 mr-2" />
                Active Workflows
              </CardTitle>
              <CardDescription>
                Real-time status of DBOS-powered compliance workflows
              </CardDescription>
            </div>
            <div className="flex items-center space-x-2">
              <div className="relative">
                <Search className="w-4 h-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
                <Input
                  placeholder="Search workflows..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-9 w-64"
                />
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {filteredWorkflows.map((workflow) => (
              <div key={workflow.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <Badge variant="outline">{workflow.id}</Badge>
                    <Badge variant="outline">{workflow.type}</Badge>
                    <Badge className={getStatusColor(workflow.status)}>
                      {getStatusIcon(workflow.status)}
                      <span className="ml-1">{workflow.status}</span>
                    </Badge>
                  </div>
                  <div className="flex space-x-2">
                    {workflow.status === 'Paused' && (
                      <Button variant="outline" size="sm">
                        <Play className="w-4 h-4 mr-1" />
                        Resume
                      </Button>
                    )}
                    {workflow.status === 'Running' && (
                      <Button variant="outline" size="sm">
                        <Pause className="w-4 h-4 mr-1" />
                        Pause
                      </Button>
                    )}
                    <Button variant="outline" size="sm">
                      <RotateCcw className="w-4 h-4 mr-1" />
                      Retry
                    </Button>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <p className="font-medium text-sm mb-1">Document/Target</p>
                    <p className="text-sm text-gray-600">{workflow.document}</p>
                  </div>
                  <div>
                    <p className="font-medium text-sm mb-1">Executor</p>
                    <p className="text-sm text-gray-600">{workflow.executor}</p>
                  </div>
                </div>

                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Progress: Step {workflow.progress === 100 ? workflow.totalSteps : Math.ceil((workflow.progress / 100) * workflow.totalSteps)}/{workflow.totalSteps} - {workflow.currentStep}</span>
                    <span>{workflow.progress}%</span>
                  </div>
                  <Progress value={workflow.progress} className="h-2" />
                </div>

                <div className="flex items-center justify-between text-xs text-gray-500 mt-3">
                  <span>Started: {workflow.startTime}</span>
                  <span>
                    {workflow.status === 'Completed' 
                      ? `Completed: ${workflow.estimatedCompletion}`
                      : workflow.status === 'Paused'
                      ? 'Paused'
                      : `ETA: ${workflow.estimatedCompletion}`
                    }
                  </span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* DBOS Performance Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>DBOS Performance Metrics</CardTitle>
            <CardDescription>
              Real-time system performance and reliability indicators
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dbosMetrics.map((metric, index) => (
                <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                  <div>
                    <p className="font-medium text-sm">{metric.metric}</p>
                    <p className="text-xs text-gray-600">{metric.description}</p>
                  </div>
                  <div className="text-right">
                    <p className="text-lg font-bold text-blue-600">{metric.value}</p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Workflow Benefits</CardTitle>
            <CardDescription>
              Advantages of DBOS-powered compliance automation
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="p-3 bg-blue-50 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-1">Durable Execution</h4>
                <p className="text-sm text-blue-700">Workflows automatically resume after interruptions</p>
              </div>
              <div className="p-3 bg-green-50 rounded-lg">
                <h4 className="font-medium text-green-900 mb-1">Managed Concurrency</h4>
                <p className="text-sm text-green-700">Optimized resource allocation and rate limiting</p>
              </div>
              <div className="p-3 bg-purple-50 rounded-lg">
                <h4 className="font-medium text-purple-900 mb-1">Real-time Monitoring</h4>
                <p className="text-sm text-purple-700">Complete visibility into workflow execution</p>
              </div>
              <div className="p-3 bg-orange-50 rounded-lg">
                <h4 className="font-medium text-orange-900 mb-1">Fault Tolerance</h4>
                <p className="text-sm text-orange-700">Built-in error handling and recovery mechanisms</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default WorkflowStatus;
